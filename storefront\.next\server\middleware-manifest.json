{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4380d7._.js", "server/edge/chunks/[root of the server]__a96925._.js", "server/edge/chunks/edge-wrapper_882b0c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(|\\\\.json|\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|assets|_next\\/static|favicon.ico|_next\\/image|images|robots.txt|public|static).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|assets|_next/static|favicon.ico|_next/image|images|robots.txt|public|static).*)"}], "wasm": [], "assets": [{"name": "server/edge/chunks/_4380d7._.js.map", "filePath": "server/edge/chunks/_4380d7._.js.map"}, {"name": "server/edge/chunks/_4380d7._.js", "filePath": "server/edge/chunks/_4380d7._.js"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js.map", "filePath": "server/edge/chunks/[root of the server]__a96925._.js.map"}, {"name": "server/edge/chunks/[root of the server]__a96925._.js", "filePath": "server/edge/chunks/[root of the server]__a96925._.js"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js.map", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js.map"}, {"name": "server/edge/chunks/edge-wrapper_882b0c.js", "filePath": "server/edge/chunks/edge-wrapper_882b0c.js"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "P05OuEt5furfyEF66ygpaKH/w9vOxc9kXCOUgMXeQGw=", "__NEXT_PREVIEW_MODE_ID": "d8e4b1a569f5d7edbad9e3387bc93109", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fcbc4b506bf30704e5fe24f43e3c8370570804aef223cc085f9929ab3e133e75", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3e51280d03f3c2b5459ed5bdb7c810df3484d65db13d6410422c529d88d43252"}}}, "sortedMiddleware": ["/"], "functions": {}}