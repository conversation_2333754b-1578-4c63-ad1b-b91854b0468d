"use client"
import { Radio, RadioGroup } from "@headlessui/react"
import { retrieveCart, setShippingMethod } from "@lib/data/cart"
import { convertCurrencyToLocale } from "@lib/util/money"
import { CheckCircleSolid } from "@medusajs/icons"
import { HttpTypes } from "@medusajs/types"
import ErrorMessage from "@modules/checkout/components/error-message"
import MedusaRadio from "@modules/common/components/radio"
import { Button } from "components/ui/button"
import { Card, CardContent, CardHeader } from "components/ui/card"
import Typography from "components/ui/typography"
import { Loader2 } from "lucide-react"
import { usePathname, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { CHECKOUT_STEP } from "utils/constant"
import {
  calculateShippingType,
  filterShippingOptions,
  getRecommendedShippingOption,
} from "utils/shipping-calculator"
import { useCartStore } from "zustand-store/cart-store"
import { useCheckoutStore } from "zustand-store/useCheckoutStore"

type ShippingProps = {
  cart: HttpTypes.StoreCart | null
  availableShippingMethods: HttpTypes.StoreCartShippingOption[] | null
}

const Shipping: React.FC<ShippingProps> = ({
  cart,
  availableShippingMethods,
}) => {
  const { t } = useTranslation("checkout")
  const { setCartCustomField } = useCartStore()
  const { selectedDistrict, setIsCalculatingShipping } = useCheckoutStore()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [shippingMethodId, setShippingMethodId] = useState<string | null>(null)
  const [filteredShippingMethods, setFilteredShippingMethods] = useState<
    HttpTypes.StoreCartShippingOption[] | null
  >(null)
  // Removed complex state management

  if (!cart) {
    return <Typography>{t("cart_not_available")}</Typography>
  }

  const router = useRouter()
  const pathname = usePathname()

  const isOpen = true

  // Removed event system - using direct cart state instead

  useEffect(() => {
    const currentShippingMethodId =
      cart.shipping_methods?.at(-1)?.shipping_option_id || null
    setShippingMethodId(currentShippingMethodId)
  }, [cart?.shipping_methods])

  // Auto-set shipping method when available methods are loaded
  useEffect(() => {
    if (
      availableShippingMethods &&
      availableShippingMethods.length > 0 &&
      !cart.shipping_methods?.length &&
      !shippingMethodId
    ) {
      const defaultMethod = availableShippingMethods[0].id
      handleSetShippingMethod(defaultMethod)
    }
  }, [availableShippingMethods, cart.shipping_methods, shippingMethodId])

  const selectedShippingMethod = (
    filteredShippingMethods || availableShippingMethods
  )?.find((method) => method.id === shippingMethodId)

  const handleEdit = () => {
    router.push(pathname + "?step=" + CHECKOUT_STEP.SHIPPING, { scroll: false })
  }

  const handleSubmit = () => {
    setIsLoading(true)
    router.push(pathname + "?step=" + CHECKOUT_STEP.PAYMENT, {
      scroll: false,
    })
  }

  const handleSetShippingMethod = async (id: string) => {
    setIsLoading(true)
    setShippingMethodId(id)

    try {
      setError(null)
      const result = await setShippingMethod({
        cartId: cart.id,
        shippingMethodId: id,
      })

      // Update cart in store immediately with API response data
      const updatedCart = result.cart
      setCartCustomField(updatedCart as any)
      window.dispatchEvent(
        new CustomEvent("shippingMethodUpdated", {
          detail: {
            shipping_total: updatedCart.shipping_total,
            shipping_methods: updatedCart.shipping_methods,
          },
        })
      )
    } catch (error) {
      console.error(error)
      setShippingMethodId(
        cart.shipping_methods?.at(-1)?.shipping_option_id || null
      )
      setError(
        error instanceof Error
          ? error.message
          : t("steps.shipping_method.error")
      )
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    setError(null)
  }, [isOpen])

  // Real-time shipping calculation when ward changes
  useEffect(() => {
    const currentWard = (cart?.shipping_address?.metadata as any)?.ward

    console.log("🔄 useEffect triggered - Ward dependency changed:", {
      selectedDistrict,
      currentWard,
      cartId: cart?.id,
      metadataWard: cart?.shipping_address?.metadata?.ward,
      fullMetadata: cart?.shipping_address?.metadata,
    })

    if (!selectedDistrict || !currentWard) {
      console.log("❌ Missing requirements:", { selectedDistrict, currentWard })
      setFilteredShippingMethods(availableShippingMethods)
      setIsCalculatingShipping(false)
      return
    }

    console.log("✅ Starting real-time calculation for:", {
      selectedDistrict,
      currentWard,
    })

    // Set loading immediately
    setIsCalculatingShipping(true)

    // Calculate immediately without debounce for real-time feel
    const calculateShipping = async () => {
      await autoSelectShippingMethod()
    }

    calculateShipping()
  }, [
    selectedDistrict,
    cart?.shipping_address?.metadata?.ward, // Only ward matters for triggering
  ])

  const autoSelectShippingMethod = async () => {
    const currentWard = (cart?.shipping_address?.metadata as any)?.ward

    if (
      !availableShippingMethods ||
      !cart ||
      !selectedDistrict ||
      !currentWard
    ) {
      setFilteredShippingMethods(availableShippingMethods)
      setIsCalculatingShipping(false)
      return
    }

    console.log("🚀 Real-time GPS calculation for:", {
      selectedDistrict,
      currentWard,
    })

    try {
      // Calculate shipping type using GPS
      const shippingType = await calculateShippingType(
        cart.shipping_address?.address_1,
        currentWard,
        selectedDistrict,
        cart.shipping_address?.province
      )

      // Filter shipping options based on calculated type
      const filtered = filterShippingOptions(
        availableShippingMethods,
        shippingType,
        cart.total || 0
      )

      setFilteredShippingMethods(filtered)

      // Auto-select recommended option
      if (filtered.length > 0) {
        const recommended = getRecommendedShippingOption(
          availableShippingMethods,
          shippingType,
          cart.total || 0
        )

        if (recommended && recommended.id !== shippingMethodId) {
          // Auto apply recommended shipping method
          try {
            await handleSetShippingMethod(recommended.id)
            // Force refresh cart to update shipping total
            const updatedCart = await retrieveCart()
            if (updatedCart) {
              setCartCustomField(updatedCart)
            }
          } catch (error) {
            console.error("❌ Shipping method failed:", error)
          }
        }
      }
    } catch (error) {
      console.error("Error in shipping calculation:", error)
      // Fallback to all available methods
      setFilteredShippingMethods(availableShippingMethods)
    } finally {
      setIsCalculatingShipping(false)
    }
  }

  // Dependencies moved to useEffect above

  // Using filtered shipping methods
  const displayShippingMethods =
    filteredShippingMethods || availableShippingMethods
  return (
    <Card className="border-none bg-white">
      <CardHeader className="flex flex-row items-center justify-between gap-x-4">
        <div className="flex items-center gap-x-2">
          <Typography variant="h4" size="lg" className="font-bold">
            {t("steps.shipping_method.title")}
          </Typography>
          {!isOpen && (cart.shipping_methods?.length ?? 0) > 0 && (
            <div>
              <CheckCircleSolid className="text-primary-main" />
            </div>
          )}
        </div>
        {JSON.stringify(cart)}
        {!isOpen && cart?.shipping_address && cart?.billing_address && (
          <Button
            onClick={handleEdit}
            variant="link"
            className="p-0 font-semibold underline underline-offset-4"
            data-testid="edit-delivery-button"
          >
            {t("edit")}
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {isOpen ? (
          <div data-testid="delivery-options-container">
            {/* Debug */}
            {/* {selectedDistrict && (
              <div className="mb-4 rounded-lg bg-blue-50 p-3">
                <Typography variant="p" size="sm" className="text-blue-700">
                  📍 Giao hàng đến:{" "}
                  <span className="font-medium">{selectedDistrict}</span>
                </Typography>
              </div>
            )} */}

            <div className="pb-8">
              {displayShippingMethods?.length === 0 ? (
                <Typography variant="p" size="sm" className="text-gray-600">
                  Không có phương thức vận chuyển nào khả dụng cho khu vực này.
                </Typography>
              ) : displayShippingMethods?.length === 1 ? (
                <Typography variant="p" size="base" className="font-semibold">
                  {displayShippingMethods[0].name} -{" "}
                  {convertCurrencyToLocale({
                    amount: displayShippingMethods[0].amount!,
                    currency_code: cart?.currency_code,
                  })}
                </Typography>
              ) : (
                <RadioGroup
                  value={shippingMethodId}
                  onChange={handleSetShippingMethod}
                >
                  {displayShippingMethods?.map((option) => (
                    <Radio
                      key={option.id}
                      value={option.id}
                      className="flex cursor-pointer items-center gap-2 py-2"
                    >
                      <MedusaRadio checked={option.id === shippingMethodId} />
                      <span>
                        {option.name} -{" "}
                        {convertCurrencyToLocale({
                          amount: option.amount!,
                          currency_code: cart?.currency_code,
                        })}
                      </span>
                    </Radio>
                  ))}
                </RadioGroup>
              )}
            </div>

            <ErrorMessage
              error={error}
              data-testid="delivery-option-error-message"
            />

            <Button
              size="lg"
              className="mt-6"
              onClick={handleSubmit}
              disabled={!shippingMethodId || isLoading}
              data-testid="submit-delivery-option-button"
            >
              {isLoading ? (
                <Loader2 className="animate-spin" />
              ) : (
                t("steps.shipping_method.continue_to_payment")
              )}
            </Button>
          </div>
        ) : (
          <div>
            <div className="text-small-regular">
              {cart && (cart.shipping_methods?.length ?? 0) > 0 && (
                <div className="flex w-1/3 flex-col">
                  <Typography variant="p" size="sm" className="font-bold">
                    {t("steps.shipping_method.method")}
                  </Typography>
                  <Typography variant="p" size="sm">
                    {selectedShippingMethod?.name}{" "}
                    {convertCurrencyToLocale({
                      amount: selectedShippingMethod?.amount!,
                      currency_code: cart?.currency_code,
                    })}
                  </Typography>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default Shipping
