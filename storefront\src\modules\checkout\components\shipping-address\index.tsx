"use client"

import { getCustomLocations } from "@lib/data/vn-locations"
import { HttpTypes } from "@medusajs/types"
import Checkbox from "@modules/common/components/checkbox"
import Divider from "@modules/common/components/divider"
import Input from "@modules/common/components/input"
import SelectNative from "@modules/common/components/select"
import Typography from "components/ui/typography"
import { mapKeys } from "lodash"
import React, { useEffect, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { TDistrict, TProvince, TWard } from "types"
import { TCustomLocation } from "types/custom-locations"
import { REGEX_PHONE } from "utils/constant"
import { useCheckoutStore } from "zustand-store/useCheckoutStore"
import AddressSelect from "../address-select"
import CountrySelect from "../country-select"

type ShippingAddressProps = {
  customer: HttpTypes.StoreCustomer | null
  cart: HttpTypes.StoreCart | null
  checkedSameAsBilling: boolean
  onChangeSameAsBilling: () => void
  generateInvoice: boolean
  toggleGenerateInvoice: () => void
  locationList?: TCustomLocation[]
}

const ShippingAddress: React.FC<ShippingAddressProps> = ({
  customer,
  cart,
  checkedSameAsBilling,
  onChangeSameAsBilling,
  generateInvoice,
  toggleGenerateInvoice,
  locationList,
}) => {
  const { t } = useTranslation("checkout")
  const {
    shippingAddress,
    setShippingAddress,
    saveAddress,
    setSaveAddress,
    setSelectedDistrict,
    setIsCalculatingShipping,
  } = useCheckoutStore()
  const [provinces, setProvinces] = useState<TProvince[]>([])
  const [districts, setDistricts] = useState<TDistrict[]>([])
  const [wards, setWards] = useState<TWard[]>([])
  const [fullNameInput, setFullNameInput] = useState("")
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({})

  const splitFullName = (fullName: string) => {
    const nameParts = fullName.trim().split(/\s+/)
    if (nameParts.length === 0) {
      return { first_name: "", last_name: "" }
    }
    const first_name = nameParts[nameParts.length - 1]
    const last_name = nameParts.slice(0, -1).join(" ")
    return { first_name, last_name }
  }

  // Initialize fullNameInput when component mounts or shipping address changes
  useEffect(() => {
    const { first_name, last_name } = shippingAddress.shipping_address
    const initialFullName =
      [last_name, first_name].filter(Boolean).join(" ") || ""
    setFullNameInput(initialFullName)
  }, [
    shippingAddress.shipping_address.first_name,
    shippingAddress.shipping_address.last_name,
  ])

  const countriesInRegion = useMemo(
    () => cart?.region?.countries?.map((c) => c.iso_2),
    [cart?.region]
  )

  const addressesInRegion = useMemo(
    () =>
      customer?.addresses?.filter(
        (a) => a.country_code && countriesInRegion?.includes(a.country_code)
      ),
    [customer?.addresses, countriesInRegion]
  )

  const [lastCartId, setLastCartId] = useState<string | undefined>(undefined)
  useEffect(() => {
    if (cart?.id && cart.id !== lastCartId) {
      setShippingAddress({
        shipping_address: {
          first_name: cart.shipping_address?.first_name || "",
          last_name: cart.shipping_address?.last_name || "",
          address_1: cart.shipping_address?.address_1 || "",
          company: cart.shipping_address?.company || "",
          postal_code: cart.shipping_address?.postal_code || "",
          province: cart.shipping_address?.province || "",
          country_code: cart.shipping_address?.country_code || "",
          phone: cart.shipping_address?.phone || "",
          metadata: {
            district: cart.shipping_address?.metadata?.district || "",
            ward: cart.shipping_address?.metadata?.ward || "",
            delivery_note: cart.shipping_address?.metadata?.delivery_note || "",
            is_generate_invoice:
              cart.shipping_address?.metadata?.is_generate_invoice || false,
            company_name: cart.shipping_address?.metadata?.company_name || "",
            company_tax_code:
              cart.shipping_address?.metadata?.company_tax_code || "",
            company_address:
              cart.shipping_address?.metadata?.company_address || "",
          },
        },
        email: cart.email || "",
      })
      setLastCartId(cart.id)
    }
  }, [cart?.id])

  useEffect(() => {
    const fetchProvinces = async () => {
      const provinceList = await getCustomLocations()
      setProvinces(provinceList)
    }
    fetchProvinces()
  }, [])

  useEffect(() => {
    if (!shippingAddress.shipping_address.province) {
      setDistricts([])
      setWards([])
      if (
        shippingAddress.shipping_address?.metadata?.district ||
        shippingAddress.shipping_address?.metadata?.ward
      ) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              district: "",
              ward: "",
            },
          },
        })
      }
      return
    }

    const fetchDistricts = async () => {
      const provinceName = shippingAddress.shipping_address.province
      const province = provinces.find((p) => p.name === provinceName)
      const districtList = province?.districts || []
      setDistricts(districtList)
      if (
        districtList.length > 0 &&
        !districtList.some(
          (d) => d.name === shippingAddress.shipping_address?.metadata?.district
        )
      ) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              district: "",
              ward: "",
            },
          },
        })
      }
    }

    fetchDistricts()
  }, [shippingAddress.shipping_address.province, provinces])

  // Cập nhật selectedDistrict khi district thay đổi
  useEffect(() => {
    const currentDistrict = shippingAddress.shipping_address?.metadata?.district
    setSelectedDistrict(currentDistrict || null)
  }, [
    shippingAddress.shipping_address?.metadata?.district,
    setSelectedDistrict,
  ])

  useEffect(() => {
    if (!shippingAddress.shipping_address?.metadata?.district) {
      setWards([])
      if (shippingAddress.shipping_address?.metadata?.ward) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              ward: "",
            },
          },
        })
      }
      return
    }

    const fetchWards = async () => {
      const districtName = shippingAddress.shipping_address?.metadata?.district
      const province = provinces.find((p) =>
        p.districts.some((d) => d.name === districtName)
      )
      const district = province?.districts.find((d) => d.name === districtName)
      const wardList = district?.wards || []
      setWards(wardList)
      if (
        wardList.length > 0 &&
        !wardList.some(
          (w) => w.name === shippingAddress.shipping_address?.metadata?.ward
        )
      ) {
        setShippingAddress({
          shipping_address: {
            ...shippingAddress.shipping_address,
            metadata: {
              ...shippingAddress.shipping_address.metadata,
              ward: "",
            },
          },
        })
      }
    }

    fetchWards()
  }, [shippingAddress.shipping_address?.metadata?.district, provinces])

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target

    if (name === "full_name") {
      setFullNameInput(value)
    } else if (name === "email") {
      setShippingAddress({
        email: value,
      })
    } else if (name.startsWith("shipping_address.metadata.")) {
      const metadataField = name.replace("shipping_address.metadata.", "")
      setShippingAddress({
        shipping_address: {
          ...shippingAddress?.shipping_address,
          metadata: {
            ...shippingAddress?.shipping_address?.metadata,
            [metadataField]: value,
          },
        },
      })
    } else {
      const fieldName = name.replace("shipping_address.", "")
      setShippingAddress({
        shipping_address: {
          ...shippingAddress?.shipping_address,
          [fieldName]: value,
        },
      })
    }
  }

  const handleBlur = (
    e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    validateField(name, value)
  }

  const handleFullNameBlur = () => {
    const { first_name, last_name } = splitFullName(fullNameInput)
    setShippingAddress({
      shipping_address: {
        ...shippingAddress?.shipping_address,
        first_name,
        last_name,
      },
    })
    // Validate full name
    validateField("full_name", fullNameInput)
  }

  // Validation functions
  const validateField = (fieldName: string, value: string) => {
    const errors = { ...validationErrors }

    switch (fieldName) {
      case "full_name":
        const { first_name, last_name } = splitFullName(value)
        if (!first_name.trim() || !last_name.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.full_name_required"
          )
        } else {
          delete errors[fieldName]
        }
        break

      case "shipping_address.phone":
        if (!value.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.phone_required"
          )
        } else if (!new RegExp(REGEX_PHONE).test(value)) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.phone_invalid"
          )
        } else {
          delete errors[fieldName]
        }
        break

      case "email":
        if (!value.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.email_required"
          )
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.email_invalid"
          )
        } else {
          delete errors[fieldName]
        }
        break

      case "shipping_address.address_1":
        if (!value.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.address_required"
          )
        } else {
          delete errors[fieldName]
        }
        break

      case "shipping_address.metadata.ward":
        if (!value.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.ward_required"
          )
        } else {
          delete errors[fieldName]
        }
        break

      case "shipping_address.metadata.district":
        if (!value.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.district_required"
          )
        } else {
          delete errors[fieldName]
        }
        break

      case "shipping_address.province":
        if (!value.trim()) {
          errors[fieldName] = t(
            "steps.delivery_address.form.validation.province_required"
          )
        } else {
          delete errors[fieldName]
        }
        break
    }

    setValidationErrors(errors)
  }

  const ErrorMessage = ({ fieldName }: { fieldName: string }) => {
    const error = validationErrors[fieldName]
    if (!error) return null

    return <div className="mt-1.5 text-[13px] text-red">{error}</div>
  }

  const setFormAddress = (
    address?: HttpTypes.StoreCartAddress,
    email?: string
  ) => {
    setShippingAddress({
      shipping_address: {
        ...shippingAddress.shipping_address,
        ...(address && {
          first_name: address.first_name || "",
          last_name: address.last_name || "",
          address_1: address.address_1 || "",
          company: address.company || "",
          postal_code: address.postal_code || "",
          province: address.province || "",
          country_code: address.country_code || "",
          phone: address.phone || "",
          metadata: {
            district: address.metadata?.district || "",
            ward: address.metadata?.ward || "",
            delivery_note: address.metadata?.delivery_note || "",
            is_generate_invoice: address.metadata?.is_generate_invoice || false,
            company_name: address.metadata?.company_name || "",
            company_tax_code: address.metadata?.company_tax_code || "",
            company_address: address.metadata?.company_address || "",
          },
        }),
      },
      email: email !== undefined ? email : shippingAddress.email,
    })
  }

  const renderAddressSelect = () => {
    if (!customer || (addressesInRegion?.length || 0) === 0) return null

    return (
      <>
        <Typography variant="p" size="sm">
          {t("steps.delivery_address.greeting", { name: customer.first_name })}
        </Typography>
        <AddressSelect
          addresses={addressesInRegion || []}
          addressInput={
            mapKeys(
              shippingAddress.shipping_address,
              (_, key) => key
            ) as HttpTypes.StoreCartAddress
          }
          onSelect={setFormAddress}
        />
        <Divider />
      </>
    )
  }

  return (
    <>
      {renderAddressSelect()}
      <div className="flex flex-col gap-4 lg:gap-6">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:gap-6">
          <div>
            <Input
              topLabel={t("steps.delivery_address.form.full_name")}
              name="full_name"
              autoComplete="name"
              value={fullNameInput}
              onChange={handleChange}
              onBlur={handleFullNameBlur}
              required
              data-testid="shipping-full-name-input"
            />
            <ErrorMessage fieldName="full_name" />
          </div>
          <div>
            <Input
              required
              type="tel"
              pattern={`${REGEX_PHONE}`}
              topLabel={t("steps.delivery_address.form.phone")}
              name="shipping_address.phone"
              autoComplete="tel"
              value={shippingAddress.shipping_address.phone || ""}
              onChange={handleChange}
              onBlur={handleBlur}
            />
            <ErrorMessage fieldName="shipping_address.phone" />
          </div>
        </div>

        <div>
          <Input
            required
            type="email"
            topLabel={t("steps.delivery_address.form.email")}
            name="email"
            autoComplete="email"
            value={shippingAddress.email || ""}
            onChange={handleChange}
            onBlur={handleBlur}
          />
          <ErrorMessage fieldName="email" />
        </div>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 md:grid-cols-3">
          <div>
            <SelectNative
              topLabel={t("steps.delivery_address.form.province")}
              required
              name="shipping_address.province"
              value={shippingAddress.shipping_address.province || ""}
              onChange={handleChange}
              onBlur={handleBlur}
              options={
                provinces.map((p) => ({
                  value: p.name,
                  label: p.name,
                })) || []
              }
            />
            <ErrorMessage fieldName="shipping_address.province" />
          </div>
          <div>
            <SelectNative
              topLabel={t("steps.delivery_address.form.district")}
              required
              name="shipping_address.metadata.district"
              value={shippingAddress.shipping_address?.metadata?.district || ""}
              onChange={handleChange}
              onBlur={handleBlur}
              options={
                districts.map((d) => ({
                  value: d.name,
                  label: d.name,
                })) || []
              }
              disabled={!shippingAddress.shipping_address?.province}
            />
            <ErrorMessage fieldName="shipping_address.metadata.district" />
          </div>
          <div>
            <SelectNative
              topLabel={t("steps.delivery_address.form.ward")}
              required
              name="shipping_address.metadata.ward"
              value={shippingAddress.shipping_address?.metadata?.ward || ""}
              onChange={(e) => {
                console.log("🚀 Ward changed to:", e.target.value)
                setIsCalculatingShipping(true)
                handleChange(e)

                // Force trigger shipping calculation after state update
                setTimeout(() => {
                  console.log("⚡ Force triggering shipping calculation...")
                  // Trigger custom event to force shipping recalculation
                  window.dispatchEvent(
                    new CustomEvent("wardChanged", {
                      detail: {
                        ward: e.target.value,
                        district:
                          shippingAddress.shipping_address?.metadata?.district,
                      },
                    })
                  )
                }, 100)
              }}
              onBlur={handleBlur}
              options={
                wards.map((w) => ({
                  value: w.name,
                  label: w.name,
                })) || []
              }
              disabled={!shippingAddress.shipping_address?.metadata?.district}
            />
            <ErrorMessage fieldName="shipping_address.metadata.ward" />
          </div>
        </div>

        <div>
          <Input
            topLabel={t("steps.delivery_address.form.address")}
            name="shipping_address.address_1"
            autoComplete="address-line1"
            value={shippingAddress.shipping_address.address_1 || ""}
            onChange={handleChange}
            onBlur={handleBlur}
            required
            data-testid="shipping-address-input"
          />
          <ErrorMessage fieldName="shipping_address.address_1" />
        </div>

        <Input
          topLabel={t("steps.delivery_address.form.note")}
          name="shipping_address.metadata.delivery_note"
          value={
            shippingAddress.shipping_address?.metadata?.delivery_note || ""
          }
          onChange={handleChange}
          data-testid="shipping-notes-input"
        />

        {/* Debug Area*/}
        {/* <ShippingInfoDisplay
          district={shippingAddress.shipping_address?.metadata?.district}
          cartTotal={cart?.total || 0}
          currencyCode={cart?.currency_code}
        /> */}

        <div className="grid grid-cols-2 gap-6">
          <CountrySelect
            required
            name="shipping_address.country_code"
            autoComplete="country"
            region={cart?.region}
            value={shippingAddress.shipping_address.country_code || ""}
            onChange={handleChange}
          />
        </div>
      </div>
      <div className="!-mt-0 grid gap-3">
        <Checkbox
          label={t("steps.delivery_address.form.save_address")}
          name="save_address"
          checked={saveAddress}
          onChange={() => setSaveAddress(!saveAddress)}
          data-testid="save-address-checkbox"
        />

        <Checkbox
          label={t("steps.delivery_address.form.generate_invoice")}
          name="shipping_address.metadata.is_generate_invoice"
          checked={generateInvoice}
          onChange={() => {
            toggleGenerateInvoice()
            setShippingAddress({
              shipping_address: {
                ...shippingAddress.shipping_address,
                metadata: {
                  ...shippingAddress.shipping_address.metadata,
                  is_generate_invoice: !generateInvoice,
                  company_name: !generateInvoice
                    ? shippingAddress.shipping_address.metadata?.company_name
                    : "",
                  company_tax_code: !generateInvoice
                    ? shippingAddress.shipping_address.metadata
                        ?.company_tax_code
                    : "",
                  company_address: !generateInvoice
                    ? shippingAddress.shipping_address.metadata?.company_address
                    : "",
                },
              },
            })
          }}
          data-testid="generate-invoice-checkbox"
        />
      </div>

      {generateInvoice && (
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6">
          <Input
            topLabel={t("steps.delivery_address.form.company_name")}
            name="shipping_address.metadata.company_name"
            value={
              shippingAddress.shipping_address?.metadata?.company_name || ""
            }
            onChange={handleChange}
            required={generateInvoice}
            data-testid="company-name-input"
          />
          <Input
            topLabel={t("steps.delivery_address.form.company_tax_code")}
            name="shipping_address.metadata.company_tax_code"
            value={
              shippingAddress.shipping_address?.metadata?.company_tax_code || ""
            }
            onChange={handleChange}
            required={generateInvoice}
            data-testid="company-tax-code-input"
          />
          <Input
            topLabel={t("steps.delivery_address.form.company_address")}
            name="shipping_address.metadata.company_address"
            value={
              shippingAddress.shipping_address?.metadata?.company_address || ""
            }
            onChange={handleChange}
            required={generateInvoice}
            data-testid="company-address-input"
          />
        </div>
      )}
    </>
  )
}

export default ShippingAddress
