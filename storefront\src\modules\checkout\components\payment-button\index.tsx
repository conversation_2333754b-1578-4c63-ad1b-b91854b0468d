"use client"

import { placeOrder, setAddresses } from "@lib/data/cart"
import { HttpTypes } from "@medusajs/types"
import { Button } from "components/ui/button"
import { Loader2 } from "lucide-react"
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { useCartStateStore } from "zustand-store/useCartStore"
import { useCheckoutStore } from "zustand-store/useCheckoutStore"
import ErrorMessage from "../error-message"

type PaymentButtonProps = {
  cart: HttpTypes.StoreCart
  "data-testid": string
}

const PaymentButton: React.FC<PaymentButtonProps> = ({
  cart,
  "data-testid": dataTestId,
}) => {
  if (!cart) {
    return <div>Loading cart...</div>
  }

  const shippingAddress = useCheckoutStore((state) => state.shippingAddress)
  const isLoading = useCheckoutStore((state) => state.isLoading)
  const setLoading = useCheckoutStore((state) => state.setLoading)
  const isCalculatingShipping = useCheckoutStore(
    (state) => state.isCalculatingShipping
  )

  const notReady =
    isShippingAddressIncomplete(shippingAddress, cart) ||
    cart.shipping_total === undefined ||
    cart.shipping_total === null ||
    cart.shipping_methods?.length === 0 ||
    isCalculatingShipping

  return (
    <div>
      <ManualTestPaymentButton
        notReady={notReady}
        data-testid={dataTestId}
        isLoading={isLoading}
        shippingAddress={shippingAddress}
        setLoading={setLoading}
        cart={cart}
      />
    </div>
  )
}

const isShippingAddressIncomplete = (
  shippingAddress: any,
  cart: HttpTypes.StoreCart
) => {
  if (!cart) return true

  const requiredFields = [
    "first_name",
    "last_name",
    "address_1",
    "country_code",
    "province",
    "phone",
  ]

  return (
    !shippingAddress?.email ||
    requiredFields.some(
      (field) => !shippingAddress?.shipping_address?.[field]
    ) ||
    !shippingAddress.shipping_address?.metadata?.district ||
    !shippingAddress.shipping_address?.metadata?.ward
  )
}

const ManualTestPaymentButton = ({
  notReady,
  "data-testid": dataTestId,
  isLoading,
  shippingAddress,
  setLoading,
  cart,
}: {
  notReady: boolean
  "data-testid"?: string
  isLoading: boolean
  shippingAddress: any
  setLoading: (loading: boolean) => void
  cart: HttpTypes.StoreCart
}) => {
  const { t } = useTranslation("checkout")
  const [submitting, setSubmitting] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const { setStep, setCompletedOrder } = useCartStateStore()

  const handlePayment = async () => {
    setSubmitting(true)
    setLoading(true)
    try {
      const formData = new FormData()
      Object.entries(shippingAddress.shipping_address).forEach(
        ([key, value]) => {
          if (key === "metadata") return
          formData.append(`shipping_address.${key}`, value as string)
        }
      )

      formData.append(
        "shipping_address.metadata.district",
        shippingAddress.shipping_address.metadata?.district || ""
      )
      formData.append(
        "shipping_address.metadata.ward",
        shippingAddress.shipping_address.metadata?.ward || ""
      )
      formData.append(
        "shipping_address.metadata.delivery_note",
        shippingAddress.shipping_address.metadata?.delivery_note || ""
      )
      formData.append(
        "shipping_address.metadata.is_generate_invoice",
        shippingAddress.shipping_address.metadata?.is_generate_invoice
          ? "true"
          : "false"
      )
      formData.append(
        "shipping_address.metadata.company_name",
        shippingAddress.shipping_address.metadata?.company_name || ""
      )
      formData.append(
        "shipping_address.metadata.company_tax_code",
        shippingAddress.shipping_address.metadata?.company_tax_code || ""
      )
      formData.append(
        "shipping_address.metadata.company_address",
        shippingAddress.shipping_address.metadata?.company_address || ""
      )
      formData.append("email", shippingAddress.email || "")
      formData.append("same_as_billing", "on")

      const addressResult = await setAddresses(null, formData)
      if (addressResult !== true) {
        throw new Error(
          typeof addressResult === "string"
            ? addressResult
            : "Failed to save shipping address"
        )
      }

      // Check and apply preorder promotion if needed
      const { retrieveCart, applyPromotions, initiatePaymentSession } =
        await import("@lib/data/cart")
      const currentCart = await retrieveCart()

      if (
        currentCart?.metadata?.preorder_promotion_intent &&
        currentCart.items?.length > 0 &&
        (!currentCart.promotions || currentCart.promotions.length === 0)
      ) {
        try {
          const promotionCode = String(
            currentCart.metadata.preorder_promotion_intent
          )
          await applyPromotions([promotionCode])
        } catch (error) {
          console.error(
            "Error applying preorder promotion before order:",
            error
          )
        }
      }

      // Ensure payment session exists before placing order
      const finalCart = await retrieveCart()
      console.log("💳 Payment session check:", {
        cartId: finalCart?.id,
        hasPaymentCollection: !!finalCart?.payment_collection,
        paymentSessions:
          finalCart?.payment_collection?.payment_sessions?.length || 0,
        sessions: finalCart?.payment_collection?.payment_sessions?.map((s) => ({
          id: s.id,
          status: s.status,
          provider_id: s.provider_id,
          amount: s.amount,
          currency_code: s.currency_code,
          data: s.data,
        })),
      })

      // Check if payment session is valid (status should be 'pending')
      const validSession =
        finalCart?.payment_collection?.payment_sessions?.find(
          (s) => s.status === "pending" && s.provider_id === "pp_system_default"
        )
      console.log("🔍 Valid session check:", {
        hasValidSession: !!validSession,
        validSessionId: validSession?.id,
        allStatuses: finalCart?.payment_collection?.payment_sessions?.map(
          (s) => s.status
        ),
      })

      if (
        !finalCart?.payment_collection?.payment_sessions?.length ||
        !validSession
      ) {
        const reason = !finalCart?.payment_collection?.payment_sessions?.length
          ? "No payment session found"
          : "No valid pending session found"
        console.log(`🔄 ${reason}, initializing...`)

        try {
          await initiatePaymentSession(finalCart!, {
            provider_id: "pp_system_default",
          })
          console.log("✅ Payment session initialized successfully")

          // Re-fetch cart to verify payment session
          const verifyCart = await retrieveCart()
          const newValidSession =
            verifyCart?.payment_collection?.payment_sessions?.find(
              (s) =>
                s.status === "pending" && s.provider_id === "pp_system_default"
            )
          console.log("🔍 Verification after init:", {
            paymentSessions:
              verifyCart?.payment_collection?.payment_sessions?.length || 0,
            hasValidSession: !!newValidSession,
            newSessionStatus: newValidSession?.status,
          })
        } catch (error) {
          console.error("❌ Failed to initialize payment session:", error)
          throw new Error(
            "Không thể khởi tạo phương thức thanh toán. Vui lòng thử lại."
          )
        }
      } else {
        console.log("✅ Valid payment session exists, proceeding...")
      }

      const { isSuccess, data, error } = await placeOrder()

      if (isSuccess && data?.type === "order") {
        setCompletedOrder(data.order)
        setStep("confirmed")

        // Refresh cart after successful order to get new cart state
        setTimeout(() => {
          const { fetchCart } = useCartStateStore.getState()
          fetchCart(true).catch(console.error)
        }, 1000)
      } else {
        console.error("Place order failed:", error)
        handleError(error)
      }
    } catch (error) {
      console.error("Caught error:", error)
      handleError(error)
    } finally {
      setSubmitting(false)
      setLoading(false)
    }
  }

  const handleError = (error: any) => {
    let errorMsg = "Đã xảy ra lỗi khi xử lý đơn hàng"

    if (typeof error === "string") {
      errorMsg = error
    } else if (error instanceof Error) {
      errorMsg = error.message
    } else if (error?.message) {
      errorMsg = error.message
    }

    // Customize error messages for specific cases
    if (errorMsg.includes("Payment sessions are required")) {
      errorMsg =
        "Chưa có phương thức thanh toán được thiết lập. Vui lòng thử lại."
    } else if (errorMsg.includes("payment_collection")) {
      errorMsg = "Lỗi xử lý thanh toán. Vui lòng kiểm tra lại thông tin."
    }

    setErrorMessage(errorMsg)
  }

  return (
    <div className="w-full">
      <Button
        disabled={notReady || submitting || isLoading}
        onClick={handlePayment}
        size="lg"
        data-testid={dataTestId}
        className="w-full"
      >
        {submitting || isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          t("steps.review.complete_order")
        )}
      </Button>
      <ErrorMessage
        error={errorMessage}
        data-testid="manual-payment-error-message"
      />
    </div>
  )
}

export default PaymentButton
