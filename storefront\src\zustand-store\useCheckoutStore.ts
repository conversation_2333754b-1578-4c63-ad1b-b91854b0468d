import { HttpTypes } from "@medusajs/types"
import { create } from "zustand"

// Helper functions for localStorage
const CHECKOUT_STORAGE_KEY = "checkout-form-data"

const saveToStorage = (data: any) => {
  try {
    localStorage.setItem(CHECKOUT_STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.warn("Failed to save checkout data to localStorage:", error)
  }
}

const loadFromStorage = () => {
  try {
    const stored = localStorage.getItem(CHECKOUT_STORAGE_KEY)
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.warn("Failed to load checkout data from localStorage:", error)
    return null
  }
}

interface CheckoutState {
  shippingAddress: {
    shipping_address: Record<string, any>
    email: string
  }
  isLoading: boolean
  isOrderCompleted: boolean
  completedOrder: HttpTypes.StoreOrder | null
  saveAddress: boolean
  selectedDistrict: string | null
  isCalculatingShipping: boolean
  setShippingAddress: (
    address: Partial<CheckoutState["shippingAddress"]>
  ) => void
  setLoading: (loading: boolean) => void
  setOrderCompleted: (order: HttpTypes.StoreOrder | null) => void
  setSaveAddress: (value: boolean) => void
  setSelectedDistrict: (district: string | null) => void
  setIsCalculatingShipping: (calculating: boolean) => void
}

export const createCheckoutStore = (cart?: HttpTypes.StoreCart | null) => {
  // Load from localStorage first, then fallback to cart
  const storedData = loadFromStorage()

  return create<CheckoutState>((set) => ({
    shippingAddress: {
      shipping_address:
        storedData?.shippingAddress?.shipping_address || cart?.shipping_address
          ? {
              first_name:
                storedData?.shippingAddress?.shipping_address?.first_name ||
                cart?.shipping_address?.first_name ||
                "",
              last_name:
                storedData?.shippingAddress?.shipping_address?.last_name ||
                cart?.shipping_address?.last_name ||
                "",
              address_1:
                storedData?.shippingAddress?.shipping_address?.address_1 ||
                cart?.shipping_address?.address_1 ||
                "",
              company:
                storedData?.shippingAddress?.shipping_address?.company ||
                cart?.shipping_address?.company ||
                "",
              postal_code:
                storedData?.shippingAddress?.shipping_address?.postal_code ||
                cart?.shipping_address?.postal_code ||
                "",
              province:
                storedData?.shippingAddress?.shipping_address?.province ||
                cart?.shipping_address?.province ||
                "",
              country_code:
                storedData?.shippingAddress?.shipping_address?.country_code ||
                cart?.shipping_address?.country_code ||
                "",
              phone:
                storedData?.shippingAddress?.shipping_address?.phone ||
                cart?.shipping_address?.phone ||
                "",
              metadata: {
                district:
                  storedData?.shippingAddress?.shipping_address?.metadata
                    ?.district ||
                  cart?.shipping_address?.metadata?.district ||
                  "",
                ward:
                  storedData?.shippingAddress?.shipping_address?.metadata
                    ?.ward ||
                  cart?.shipping_address?.metadata?.ward ||
                  "",
              },
            }
          : {},
      email: cart?.email || "",
    },
    isLoading: false,
    isOrderCompleted: false,
    completedOrder: null,
    saveAddress: false,
    selectedDistrict:
      storedData?.selectedDistrict ||
      (cart?.shipping_address?.metadata?.district as string) ||
      null,
    isCalculatingShipping: false,
    setSaveAddress: (value) => set({ saveAddress: value }),
    setSelectedDistrict: (district) =>
      set((state) => {
        // Save to localStorage when district changes
        const newState = { selectedDistrict: district }
        saveToStorage({
          shippingAddress: state.shippingAddress,
          selectedDistrict: district,
        })
        return newState
      }),
    setIsCalculatingShipping: (calculating) =>
      set({ isCalculatingShipping: calculating }),
    setShippingAddress: (address) =>
      set((state) => {
        const newShippingAddress = {
          shipping_address: {
            ...state.shippingAddress.shipping_address,
            ...(address.shipping_address || {}),
          },
          email:
            address.email !== undefined
              ? address.email
              : state.shippingAddress.email,
        }
        if (
          JSON.stringify(newShippingAddress) ===
          JSON.stringify(state.shippingAddress)
        ) {
          return state
        }

        // Save to localStorage when shipping address changes
        const newState = { shippingAddress: newShippingAddress }
        saveToStorage({
          shippingAddress: newShippingAddress,
          selectedDistrict: state.selectedDistrict,
        })

        return newState
      }),
    setLoading: (loading) => set({ isLoading: loading }),
    setOrderCompleted: (order) =>
      set({ isOrderCompleted: !!order, completedOrder: order }),
  }))
}

export const useCheckoutStore = createCheckoutStore()
