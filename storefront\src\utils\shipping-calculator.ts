import { HttpTypes } from "@medusajs/types"

// Store coordinates (Efruit store in Phu Nhuan District)
const STORE_COORDINATES = {
  lat: 10.7973676,
  lng: 106.6669566,
}

// Distance threshold for shipping calculation (7km)
const DISTANCE_THRESHOLD_KM = 7

// Free shipping threshold (1,000,000 VND)
const FREE_SHIPPING_THRESHOLD = 1000000

// Cache removed - always calculate real-time

/**
 * Calculate distance between two coordinates using Haversine formula
 */
function getDistanceFromLatLonInKm(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371 // Radius of the earth in km
  const dLat = deg2rad(lat2 - lat1)
  const dLon = deg2rad(lon2 - lon1)
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(deg2rad(lat1)) *
      Math.cos(deg2rad(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const d = R * c // Distance in km
  return d
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180)
}

/**
 * Get coordinates from address using Nominatim API
 */
async function getCoordinatesFromAddress(
  address: string
): Promise<{ lat: number; lng: number } | null> {
  // Timeout helper
  function fetchWithTimeout(resource: RequestInfo, options: any = {}) {
    const { timeout = 6000 } = options // 6s timeout
    return Promise.race([
      fetch(resource, options),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout geocoding")), timeout)
      ),
    ])
  }
  try {
    const apiUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
      address
    )}&limit=1`

    console.log(`🌐 Nominatim API call:`, apiUrl)

    const response = (await fetchWithTimeout(apiUrl, {
      timeout: 6000,
    })) as Response

    if (!response.ok) {
      return null
    }

    const data = await response.json()
    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lng: parseFloat(data[0].lon),
      }
    }

    return null
  } catch (error) {
    console.error("Geocoding error:", error)
    return null
  }
}

/**
 * Build full address string for geocoding
 */
export function buildFullAddress(
  address1?: string,
  ward?: string,
  district?: string,
  province?: string
): string {
  const parts = [address1, ward, district].filter(Boolean)

  // Force add "Thành phố Hồ Chí Minh" for better geocoding accuracy in HCMC
  // This prevents confusion with same district/ward names in other provinces
  const fullAddress = parts.join(", ")
  return fullAddress ? `${fullAddress}, Thành phố Hồ Chí Minh, Việt Nam` : ""
}

/**
 * Check if cart is eligible for free shipping
 */
export function isEligibleForFreeShipping(cartTotal: number): boolean {
  return cartTotal >= FREE_SHIPPING_THRESHOLD
}

/**
 * Calculate shipping type based on address
 * Returns "near" if < 7km, "far" if >= 7km
 */
export async function calculateShippingType(
  address1?: string,
  ward?: string,
  district?: string,
  province?: string
): Promise<"near" | "far"> {
  // No cache - always calculate real-time
  console.log(`� Real-time calculation for: ${ward}, ${district}`)

  // Try multiple address levels for better success rate
  const addressLevels = [
    // Level 1: Full address (most accurate)
    buildFullAddress(address1, ward, district, province),
    // Level 2: Without specific address (more reliable)
    buildFullAddress(undefined, ward, district, province),
    // Level 3: Just district and province (fallback)
    buildFullAddress(undefined, undefined, district, province),
  ].filter(Boolean)

  for (let index = 0; index < addressLevels.length; index++) {
    const fullAddress = addressLevels[index]
    console.log(`🔍 GPS attempt ${index + 1}: ${fullAddress}`)

    const coordinates = await getCoordinatesFromAddress(fullAddress)

    if (coordinates) {
      console.log(`✅ GPS success on attempt ${index + 1}`)

      // Calculate distance and return result
      const distance = getDistanceFromLatLonInKm(
        STORE_COORDINATES.lat,
        STORE_COORDINATES.lng,
        coordinates.lat,
        coordinates.lng
      )

      const shippingType = distance <= DISTANCE_THRESHOLD_KM ? "near" : "far"

      console.log(`📍 Distance: ${distance.toFixed(2)}km → ${shippingType}`)

      return shippingType
    }

    console.log(`❌ GPS failed on attempt ${index + 1}`)
  }

  // All GPS attempts failed
  console.log(`❌ All GPS attempts failed, defaulting to far`)

  return "far"
}

/**
 * Filter shipping options based on shipping type
 */
export function filterShippingOptions(
  shippingOptions: HttpTypes.StoreCartShippingOption[],
  shippingType: "near" | "far",
  cartTotal: number
): HttpTypes.StoreCartShippingOption[] {
  if (!shippingOptions || shippingOptions.length === 0) {
    return []
  }

  // Check for free shipping first
  if (isEligibleForFreeShipping(cartTotal)) {
    const freeOptions = shippingOptions.filter((option) => option.amount === 0)
    if (freeOptions.length > 0) {
      return freeOptions
    }
  }

  // Filter based on shipping type using amount
  const filteredOptions = shippingOptions.filter((option: any) => {
    const metadata = option.metadata || {}

    // Priority 1: Use metadata if available
    if (metadata.shipping_type) {
      return metadata.shipping_type === shippingType
    }

    // Priority 2: Filter by amount (your actual setup)
    if (shippingType === "near") {
      // Near: 30k VND or less (excluding free shipping)
      return option.amount > 0 && option.amount <= 30000
    } else {
      // Far: 50k VND (excluding free shipping)
      return option.amount > 30000
    }
  })

  return filteredOptions.length > 0 ? filteredOptions : shippingOptions
}

/**
 * Get recommended shipping option (cheapest for the shipping type)
 */
export function getRecommendedShippingOption(
  shippingOptions: HttpTypes.StoreCartShippingOption[],
  shippingType: "near" | "far",
  cartTotal: number
): HttpTypes.StoreCartShippingOption | null {
  const filtered = filterShippingOptions(
    shippingOptions,
    shippingType,
    cartTotal
  )

  if (filtered.length === 0) {
    return null
  }

  // Return the cheapest option
  const recommended = filtered.reduce((cheapest, current) => {
    return current.amount < cheapest.amount ? current : cheapest
  })

  return recommended
}
